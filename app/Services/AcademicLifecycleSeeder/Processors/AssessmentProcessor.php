<?php

declare(strict_types=1);

namespace App\Services\AcademicLifecycleSeeder\Processors;

use App\Models\CourseOffering;
use App\Models\AssessmentComponent;
use App\Models\AssessmentComponentDetail;
use App\Models\AssessmentComponentDetailScore;
use App\Models\CourseRegistration;
use App\Models\AcademicRecord;
use App\Models\Syllabus;
use App\Services\AcademicLifecycleSeeder\SeederConfiguration;
use App\Services\AcademicLifecycleSeeder\Generators\RealisticDataGenerator;
use App\Services\AcademicLifecycleSeeder\Exceptions\PhaseExecutionException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * AssessmentProcessor
 * 
 * Generates assessment component details, student scores, and calculates final grades.
 * 
 * Requirements:
 * - Generate assessment_component_details for each assessment component
 * - Create assessment_component_detail_scores for each student and assessment
 * - Simulate realistic performance with submission times, scores, lateness, bonus points
 * - Apply grade distribution patterns (A: 15%, B: 35%, C: 35%, D: 10%, F: 5%)
 * - Calculate final_percentage from weighted assessment scores
 * - Determine final_letter_grade and grade_points based on percentage
 * - Update academic records with completion_status and final grades
 */
class AssessmentProcessor extends AbstractPhaseProcessor
{
    private RealisticDataGenerator $dataGenerator;

    public function __construct()
    {
        parent::__construct('assessment_grading');
        $this->dataGenerator = new RealisticDataGenerator();
    }

    /**
     * Process the assessment and grading phase
     */
    protected function process(SeederConfiguration $configuration): bool
    {
        Log::info('Starting assessment and grading phase');

        foreach ($configuration->targetSemesters as $semesterCode) {
            $this->processSemesterAssessments($semesterCode, $configuration);
        }

        Log::info('Assessment and grading phase completed successfully');
        return true;
    }

    /**
     * Process assessments for a specific semester
     */
    private function processSemesterAssessments(string $semesterCode, SeederConfiguration $configuration): void
    {
        // Get course offerings for this semester
        $courseOfferings = CourseOffering::whereHas('semester', function ($query) use ($semesterCode) {
            $query->where('code', $semesterCode);
        })
        ->where('is_active', true)
        ->with(['semester', 'curriculumUnit.unit', 'syllabus.assessmentComponents'])
        ->get();

        if ($courseOfferings->isEmpty()) {
            Log::warning("No course offerings found for semester {$semesterCode}");
            return;
        }

        $totalAssessmentDetails = 0;
        $totalScores = 0;
        $totalGradesCalculated = 0;

        foreach ($courseOfferings as $courseOffering) {
            $result = $this->processAssessmentsForCourse($courseOffering, $configuration);
            $totalAssessmentDetails += $result['assessment_details_created'];
            $totalScores += $result['scores_created'];
            $totalGradesCalculated += $result['grades_calculated'];
        }

        $this->addRecordCount('assessment_component_details', $totalAssessmentDetails);
        $this->addRecordCount('assessment_scores', $totalScores);
        $this->addRecordCount('final_grades_calculated', $totalGradesCalculated);

        Log::info("Assessments completed for semester {$semesterCode}", [
            'assessment_details_created' => $totalAssessmentDetails,
            'scores_created' => $totalScores,
            'grades_calculated' => $totalGradesCalculated,
        ]);
    }

    /**
     * Process assessments for a specific course offering
     */
    public function processAssessmentsForCourse(CourseOffering $courseOffering, SeederConfiguration $configuration): array
    {
        $syllabus = $courseOffering->syllabus;
        
        if (!$syllabus) {
            Log::warning("No syllabus found for course offering {$courseOffering->id}");
            return ['assessment_details_created' => 0, 'scores_created' => 0, 'grades_calculated' => 0];
        }

        $assessmentComponents = $syllabus->assessmentComponents;
        
        if ($assessmentComponents->isEmpty()) {
            Log::warning("No assessment components found for syllabus {$syllabus->id}");
            return ['assessment_details_created' => 0, 'scores_created' => 0, 'grades_calculated' => 0];
        }

        // Step 1: Create assessment component details
        $assessmentDetails = $this->createAssessmentComponentDetails($assessmentComponents, $courseOffering);
        
        // Step 2: Generate student scores
        $scores = $this->generateStudentScores($assessmentDetails, $courseOffering, $configuration);
        
        // Step 3: Calculate final grades
        $gradesCalculated = $this->calculateFinalGrades($courseOffering);

        return [
            'assessment_details_created' => $assessmentDetails->count(),
            'scores_created' => $scores,
            'grades_calculated' => $gradesCalculated,
        ];
    }

    /**
     * Create assessment component details for each assessment component
     * Requirements: 4.1 - Generate assessment_component_details with realistic due dates and weight distribution
     */
    public function createAssessmentComponentDetails(Collection $assessmentComponents, CourseOffering $courseOffering): Collection
    {
        $createdDetails = collect();
        $semester = $courseOffering->semester;

        foreach ($assessmentComponents as $component) {
            // Check if details already exist
            $existingDetails = AssessmentComponentDetail::where('assessment_component_id', $component->id)->count();
            
            if ($existingDetails > 0) {
                Log::debug("Assessment details already exist for component {$component->id}");
                continue;
            }

            // Create assessment details based on component type
            $details = $this->generateAssessmentDetails($component, $semester);
            
            foreach ($details as $detailData) {
                $detail = AssessmentComponentDetail::create([
                    'assessment_component_id' => $component->id,
                    'name' => $detailData['name'],
                    'description' => $detailData['description'],
                    'due_date' => $detailData['due_date'],
                    'weight_percentage' => $detailData['weight_percentage'],
                    'max_score' => $detailData['max_score'],
                    'is_group_assessment' => $detailData['is_group_assessment'],
                    'submission_type' => $detailData['submission_type'],
                ]);
                
                $createdDetails->push($detail);
            }
        }

        Log::info("Created {$createdDetails->count()} assessment component details for {$courseOffering->curriculumUnit->unit->code}");
        
        return $createdDetails;
    }

    /**
     * Generate assessment details based on component type
     */
    private function generateAssessmentDetails(AssessmentComponent $component, $semester): array
    {
        $details = [];
        $componentWeight = $component->weight_percentage;
        
        switch ($component->type) {
            case 'assignment':
                // Split into multiple assignments
                $assignmentCount = mt_rand(2, 4);
                $weightPerAssignment = $componentWeight / $assignmentCount;
                
                for ($i = 1; $i <= $assignmentCount; $i++) {
                    $details[] = [
                        'name' => "Assignment {$i}",
                        'description' => "Assignment {$i} for {$component->name}",
                        'due_date' => $this->generateDueDate($semester, $i, $assignmentCount),
                        'weight_percentage' => round($weightPerAssignment, 1),
                        'max_score' => 100,
                        'is_group_assessment' => mt_rand(0, 1) === 1,
                        'submission_type' => 'online',
                    ];
                }
                break;
                
            case 'exam':
                $details[] = [
                    'name' => 'Final Exam',
                    'description' => "Final examination for {$component->name}",
                    'due_date' => Carbon::parse($semester->end_date)->subWeeks(1),
                    'weight_percentage' => $componentWeight,
                    'max_score' => 100,
                    'is_group_assessment' => false,
                    'submission_type' => 'in_person',
                ];
                break;
                
            case 'quiz':
                // Multiple quizzes
                $quizCount = mt_rand(3, 6);
                $weightPerQuiz = $componentWeight / $quizCount;
                
                for ($i = 1; $i <= $quizCount; $i++) {
                    $details[] = [
                        'name' => "Quiz {$i}",
                        'description' => "Quiz {$i} for {$component->name}",
                        'due_date' => $this->generateDueDate($semester, $i, $quizCount),
                        'weight_percentage' => round($weightPerQuiz, 1),
                        'max_score' => 100,
                        'is_group_assessment' => false,
                        'submission_type' => 'online',
                    ];
                }
                break;
                
            default:
                // Generic assessment
                $details[] = [
                    'name' => $component->name,
                    'description' => "Assessment for {$component->name}",
                    'due_date' => $this->generateDueDate($semester, 1, 1),
                    'weight_percentage' => $componentWeight,
                    'max_score' => 100,
                    'is_group_assessment' => false,
                    'submission_type' => 'online',
                ];
                break;
        }

        return $details;
    }

    /**
     * Generate realistic due date for assessment
     */
    private function generateDueDate($semester, int $itemNumber, int $totalItems): Carbon
    {
        $semesterStart = Carbon::parse($semester->start_date);
        $semesterEnd = Carbon::parse($semester->end_date);
        
        // Distribute assessments throughout the semester
        $semesterDuration = $semesterStart->diffInDays($semesterEnd);
        $intervalDays = $semesterDuration / ($totalItems + 1);
        
        $dueDate = $semesterStart->copy()->addDays($intervalDays * $itemNumber);
        
        // Ensure it's a weekday
        while ($dueDate->isWeekend()) {
            $dueDate->addDay();
        }
        
        return $dueDate;
    }

    /**
     * Generate student scores for assessment details
     * Requirements: 4.2 - Simulate realistic performance with grade distribution patterns
     */
    public function generateStudentScores(Collection $assessmentDetails, CourseOffering $courseOffering, SeederConfiguration $configuration): int
    {
        // Get all students registered for this course
        $registrations = CourseRegistration::where('course_offering_id', $courseOffering->id)
            ->with('student')
            ->get();

        if ($registrations->isEmpty()) {
            Log::warning("No registrations found for course offering {$courseOffering->id}");
            return 0;
        }

        $totalScores = 0;

        foreach ($assessmentDetails as $detail) {
            foreach ($registrations as $registration) {
                $this->createStudentScore($detail, $registration, $configuration);
                $totalScores++;
            }
        }

        Log::info("Created {$totalScores} assessment scores for {$courseOffering->curriculumUnit->unit->code}");
        
        return $totalScores;
    }

    /**
     * Create a student score for an assessment detail
     */
    private function createStudentScore(AssessmentComponentDetail $detail, CourseRegistration $registration, SeederConfiguration $configuration): AssessmentComponentDetailScore
    {
        // Generate realistic score based on grade distribution
        $gradeDistribution = $configuration->gradeDistribution ?? [
            'A' => 15, 'B' => 35, 'C' => 35, 'D' => 10, 'F' => 5
        ];
        
        $scoreRange = $this->getScoreRangeFromDistribution($gradeDistribution);
        $score = mt_rand($scoreRange['min'], $scoreRange['max']);
        
        // Generate submission details
        $submissionTime = $this->generateSubmissionTime($detail);
        $isLate = $submissionTime->gt($detail->due_date);
        $latePenalty = $isLate ? mt_rand(5, 15) : 0; // 5-15% penalty for late submission
        
        $finalScore = max(0, $score - $latePenalty);

        return AssessmentComponentDetailScore::create([
            'assessment_component_detail_id' => $detail->id,
            'student_id' => $registration->student_id,
            'score' => $finalScore,
            'max_score' => $detail->max_score,
            'submission_time' => $submissionTime,
            'is_late' => $isLate,
            'late_penalty' => $latePenalty,
            'bonus_points' => mt_rand(0, 1) === 1 ? mt_rand(1, 5) : 0, // Random bonus points
            'feedback' => 'Generated by Academic Lifecycle Seeder',
        ]);
    }

    /**
     * Get score range based on grade distribution
     */
    private function getScoreRangeFromDistribution(array $gradeDistribution): array
    {
        $random = mt_rand(1, 100);
        $cumulative = 0;
        
        foreach ($gradeDistribution as $grade => $percentage) {
            $cumulative += $percentage;
            if ($random <= $cumulative) {
                return match($grade) {
                    'A' => ['min' => 85, 'max' => 100],
                    'B' => ['min' => 75, 'max' => 84],
                    'C' => ['min' => 65, 'max' => 74],
                    'D' => ['min' => 50, 'max' => 64],
                    'F' => ['min' => 0, 'max' => 49],
                    default => ['min' => 65, 'max' => 74],
                };
            }
        }
        
        return ['min' => 65, 'max' => 74]; // Default to C range
    }

    /**
     * Generate realistic submission time
     */
    private function generateSubmissionTime(AssessmentComponentDetail $detail): Carbon
    {
        $dueDate = Carbon::parse($detail->due_date);
        
        // 80% submit on time, 20% submit late
        if (mt_rand(1, 100) <= 80) {
            // Submit 1-7 days before due date
            return $dueDate->copy()->subDays(mt_rand(1, 7));
        } else {
            // Submit 1-3 days after due date
            return $dueDate->copy()->addDays(mt_rand(1, 3));
        }
    }

    /**
     * Calculate final grades for all students in a course
     * Requirements: 4.3 - Calculate final_percentage and update academic records
     */
    public function calculateFinalGrades(CourseOffering $courseOffering): int
    {
        $registrations = CourseRegistration::where('course_offering_id', $courseOffering->id)
            ->with('student')
            ->get();

        $gradesCalculated = 0;

        foreach ($registrations as $registration) {
            $finalPercentage = $this->calculateStudentFinalPercentage($registration, $courseOffering);
            $letterGrade = $this->convertPercentageToLetterGrade($finalPercentage);
            $gradePoints = $this->convertLetterGradeToPoints($letterGrade);
            
            // Update academic record
            $this->updateAcademicRecord($registration, $finalPercentage, $letterGrade, $gradePoints);
            $gradesCalculated++;
        }

        Log::info("Calculated final grades for {$gradesCalculated} students in {$courseOffering->curriculumUnit->unit->code}");
        
        return $gradesCalculated;
    }

    /**
     * Calculate student's final percentage from weighted assessment scores
     */
    private function calculateStudentFinalPercentage(CourseRegistration $registration, CourseOffering $courseOffering): float
    {
        $syllabus = $courseOffering->syllabus;
        $totalWeightedScore = 0;
        $totalWeight = 0;

        foreach ($syllabus->assessmentComponents as $component) {
            $componentDetails = $component->assessmentComponentDetails;
            $componentScore = 0;
            $componentWeight = 0;

            foreach ($componentDetails as $detail) {
                $score = AssessmentComponentDetailScore::where('assessment_component_detail_id', $detail->id)
                    ->where('student_id', $registration->student_id)
                    ->first();

                if ($score) {
                    $percentage = ($score->score / $score->max_score) * 100;
                    $componentScore += $percentage * ($detail->weight_percentage / 100);
                    $componentWeight += $detail->weight_percentage;
                }
            }

            if ($componentWeight > 0) {
                $totalWeightedScore += $componentScore * ($component->weight_percentage / $componentWeight);
                $totalWeight += $component->weight_percentage;
            }
        }

        return $totalWeight > 0 ? round($totalWeightedScore, 2) : 0;
    }

    /**
     * Convert percentage to letter grade
     */
    private function convertPercentageToLetterGrade(float $percentage): string
    {
        return match(true) {
            $percentage >= 85 => 'A',
            $percentage >= 75 => 'B',
            $percentage >= 65 => 'C',
            $percentage >= 50 => 'D',
            default => 'F',
        };
    }

    /**
     * Convert letter grade to grade points
     */
    private function convertLetterGradeToPoints(string $letterGrade): float
    {
        return match($letterGrade) {
            'A' => 4.0,
            'B' => 3.0,
            'C' => 2.0,
            'D' => 1.0,
            'F' => 0.0,
            default => 0.0,
        };
    }

    /**
     * Update academic record with final grades
     */
    private function updateAcademicRecord(CourseRegistration $registration, float $finalPercentage, string $letterGrade, float $gradePoints): void
    {
        AcademicRecord::where('student_id', $registration->student_id)
            ->where('course_offering_id', $registration->course_offering_id)
            ->where('semester_id', $registration->semester_id)
            ->update([
                'final_percentage' => $finalPercentage,
                'final_letter_grade' => $letterGrade,
                'grade_points' => $gradePoints,
                'completion_status' => $letterGrade === 'F' ? 'failed' : 'completed',
                'grade_status' => $letterGrade === 'F' ? 'failed' : 'passed',
                'completion_date' => now(),
                'grade_finalized_date' => now(),
            ]);
    }

    /**
     * Validate prerequisites for this phase
     */
    public function validatePrerequisites(): bool
    {
        // Check if we have assessment components
        $componentsCount = AssessmentComponent::count();
        if ($componentsCount === 0) {
            Log::warning('No assessment components found for assessment processing');
            return false;
        }

        // Check if we have course registrations
        $registrationsCount = CourseRegistration::count();
        if ($registrationsCount === 0) {
            Log::warning('No course registrations found for assessment processing');
            return false;
        }

        Log::info("Assessment prerequisites validated", [
            'assessment_components' => $componentsCount,
            'course_registrations' => $registrationsCount,
        ]);

        return true;
    }

    /**
     * Rollback the assessment processing phase
     */
    public function rollback(): void
    {
        Log::info("Rolling back assessment processing phase");
        
        // In a real implementation, you would:
        // 1. Delete assessment component details created by this seeder
        // 2. Delete assessment scores created by this seeder
        // 3. Reset academic record grades to in_progress status
        
        Log::warning("Assessment processing rollback requested - manual cleanup may be required");
    }
}
