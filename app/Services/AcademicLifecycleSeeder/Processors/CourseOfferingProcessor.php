<?php

namespace App\Services\AcademicLifecycleSeeder\Processors;

use App\Models\CourseOffering;
use App\Models\CurriculumUnit;
use App\Models\Semester;
use App\Models\Student;
use App\Models\Enrollment;
use App\Models\Lecture;
use App\Models\Syllabus;
use App\Models\AssessmentComponent;
use App\Models\Building;
use App\Models\Room;
use App\Services\AcademicLifecycleSeeder\SeederConfiguration;
use App\Services\AcademicLifecycleSeeder\Generators\RealisticDataGenerator;
use App\Services\AcademicLifecycleSeeder\Exceptions\PhaseExecutionException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CourseOfferingProcessor extends AbstractPhaseProcessor
{
    private RealisticDataGenerator $dataGenerator;

    public function __construct()
    {
        parent::__construct('course_offerings');
        $this->dataGenerator = new RealisticDataGenerator();
    }

    /**
     * Process the course offering generation phase
     */
    protected function process(SeederConfiguration $configuration): bool
    {
        Log::info('Starting course offering generation');

        foreach ($configuration->targetSemesters as $semesterCode) {
            $semester = $this->getSemester($semesterCode);
            if (!$semester) {
                throw new PhaseExecutionException(
                    "Semester not found: {$semesterCode}",
                    0,
                    null,
                    $this->phaseName
                );
            }

            $this->processSemester($semester, $configuration);
        }

        return true;
    }

    /**
     * Calculate student's current semester number based on admission date
     * Requirements: 1.2 - Calculate by comparing admission_date with start_date of all defined semesters
     * up to and including the currently active semester
     */
    public function calculateStudentSemesterNumber(Student $student): int
    {
        $admissionDate = $student->admission_date;

        // Get the currently active semester
        $activeSemester = Semester::where('is_active', true)
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->first();

        if (!$activeSemester) {
            // If no active semester, use the latest semester
            $activeSemester = Semester::orderBy('start_date', 'desc')->first();
        }

        if (!$activeSemester) {
            return 1; // Default to first semester if no semesters found
        }

        // Get all semesters from admission date up to and including the active semester
        $semesters = Semester::where('start_date', '>=', $admissionDate)
            ->where('start_date', '<=', $activeSemester->start_date)
            ->orderBy('start_date', 'asc')
            ->get();

        // The semester number is the count of semesters since admission
        $semesterNumber = $semesters->count();

        // Ensure minimum semester number is 1
        return max(1, $semesterNumber);
    }

    /**
     * Get enrollment demand - which curriculum units have enrolled students
     * CRITICAL METHOD: Queries enrollments to determine which curriculum units need course offerings
     * Requirements: Query enrollments table to identify curriculum units with enrolled students
     */
    public function getEnrollmentDemand(Semester $semester): Collection
    {
        // Query enrollments to find curriculum units with enrolled students
        $enrollmentDemand = Enrollment::where('enrollments.semester_id', $semester->id)
            ->where('enrollments.status', 'in_progress')
            ->join('curriculum_units', function ($join) {
                $join->on('enrollments.curriculum_version_id', '=', 'curriculum_units.curriculum_version_id')
                     ->whereColumn('enrollments.semester_number', '=', 'curriculum_units.semester_number');
            })
            ->groupBy('curriculum_units.id')
            ->selectRaw('curriculum_units.id as curriculum_unit_id, COUNT(enrollments.id) as student_count')
            ->get();

        Log::info("Enrollment demand analysis", [
            'semester' => $semester->code,
            'curriculum_units_with_demand' => $enrollmentDemand->count(),
            'total_enrolled_students' => $enrollmentDemand->sum('student_count'),
        ]);

        return $enrollmentDemand;
    }

    /**
     * Fetch curriculum units that match student's curriculum_version_id and calculated semester_number
     * Requirements: 1.3 - Fetch curriculum units matching student's curriculum_version_id and semester_number
     */
    public function fetchCurriculumUnits(Student $student, int $semesterNumber): Collection
    {
        return CurriculumUnit::where('curriculum_version_id', $student->curriculum_version_id)
            ->where('semester_number', $semesterNumber)
            ->with(['unit', 'curriculumVersion'])
            ->get();
    }

    /**
     * Generate course offerings ONLY for curriculum units with enrolled students
     * CRITICAL CHANGE: Enrollment-driven approach - only create offerings for units with demand
     * Requirements: 1.1 - Generate course offerings based on enrollment demand
     */
    public function generateCourseOfferings(Semester $semester, SeederConfiguration $configuration): Collection
    {
        Log::info("Generating enrollment-driven course offerings for semester: {$semester->code}");

        // Step 1: Get enrollment demand - which curriculum units have enrolled students
        $enrollmentDemand = $this->getEnrollmentDemand($semester);

        if ($enrollmentDemand->isEmpty()) {
            Log::warning("No enrollment demand found for semester {$semester->code}");
            return collect();
        }

        Log::info("Found enrollment demand for {$enrollmentDemand->count()} curriculum units");

        // Step 2: Create course offerings ONLY for curriculum units with enrolled students
        $createdOfferings = collect();

        foreach ($enrollmentDemand as $demand) {
            $curriculumUnit = CurriculumUnit::find($demand->curriculum_unit_id);

            if (!$curriculumUnit) {
                Log::warning("Curriculum unit not found: {$demand->curriculum_unit_id}");
                continue;
            }

            // Create course offering if it doesn't exist
            $courseOffering = $this->createCourseOfferingIfDemandExists(
                $curriculumUnit,
                $semester,
                $demand->student_count,
                $configuration
            );

            if ($courseOffering && $courseOffering->wasRecentlyCreated) {
                $createdOfferings->push($courseOffering);
                Log::info("Created course offering for {$curriculumUnit->unit->code} with {$demand->student_count} enrolled students");
            }
        }

        Log::info("Created {$createdOfferings->count()} enrollment-driven course offerings");
        return $createdOfferings;
    }

    /**
     * Create course offering if demand exists (enrollment-driven approach)
     * CRITICAL METHOD: Only creates offerings when students are enrolled
     * Requirements: 1.4 - Create course offering based on enrollment demand
     */
    public function createCourseOfferingIfDemandExists(CurriculumUnit $curriculumUnit, Semester $semester, int $demandCount, SeederConfiguration $configuration): ?CourseOffering
    {
        // Check if course offering already exists
        $existingOffering = CourseOffering::where('curriculum_unit_id', $curriculumUnit->id)
            ->where('semester_id', $semester->id)
            ->first();

        if ($existingOffering) {
            Log::debug("Course offering already exists for {$curriculumUnit->unit->code}");
            return $existingOffering;
        }

        // Create new course offering with capacity based on demand
        $lecturer = $this->assignLecturer($curriculumUnit);

        // Set capacity based on demand with some buffer
        $maxCapacity = max(60, ceil($demandCount * 1.2)); // 20% buffer over demand

        $courseOfferingData = [
            'semester_id' => $semester->id,
            'curriculum_unit_id' => $curriculumUnit->id,
            'lecture_id' => $lecturer?->id,
            'section_code' => null,
            'max_capacity' => $maxCapacity,
            'current_enrollment' => 0,
            'waitlist_capacity' => intval($maxCapacity * 0.2), // 20% of max capacity
            'current_waitlist' => 0,
            'delivery_mode' => 'in_person',
            'is_active' => true,
            'enrollment_status' => 'open',
            'registration_start_date' => $semester->enrollment_start_date,
            'registration_end_date' => $semester->enrollment_end_date,
        ];

        Log::info("Creating enrollment-driven course offering", [
            'unit_code' => $curriculumUnit->unit->code,
            'demand_count' => $demandCount,
            'max_capacity' => $maxCapacity,
        ]);

        try {
            $courseOffering = CourseOffering::create($courseOfferingData);

            // Set up additional course offering data
            $this->assignLecturers(collect([$courseOffering]));
            $this->createSyllabusData($courseOffering);
            $this->assignScheduleAndLocation($courseOffering, $configuration);

            return $courseOffering;
        } catch (\Exception $e) {
            Log::error("Failed to create enrollment-driven course offering", [
                'error' => $e->getMessage(),
                'unit_code' => $curriculumUnit->unit->code,
            ]);
            throw $e;
        }
    }

    /**
     * Create course offering if it doesn't exist for the current semester
     * Requirements: 1.4 - Create course offering with default values when curriculum_unit doesn't have one
     */
    public function createCourseOfferingIfNotExists(CurriculumUnit $curriculumUnit, Semester $semester, SeederConfiguration $configuration): ?CourseOffering
    {
        // Check if course offering already exists
        $existingOffering = CourseOffering::where('curriculum_unit_id', $curriculumUnit->id)
            ->where('semester_id', $semester->id)
            ->first();

        if ($existingOffering) {
            return $existingOffering;
        }

        // Create new course offering with default values
        $lecturer = $this->assignLecturer($curriculumUnit);

        $courseOfferingData = [
            'semester_id' => $semester->id,
            'curriculum_unit_id' => $curriculumUnit->id,
            'lecture_id' => $lecturer?->id,
            'section_code' => null,
            'max_capacity' => 60, // Default as per requirements
            'current_enrollment' => 0,
            'waitlist_capacity' => 12, // 20% of max capacity
            'current_waitlist' => 0,
            'delivery_mode' => 'in_person', // Default as per requirements
            'is_active' => true,
            'enrollment_status' => 'open', // Default as per requirements
            'registration_start_date' => $semester->enrollment_start_date,
            'registration_end_date' => $semester->enrollment_end_date,
        ];

        Log::info("Creating course offering with data", $courseOfferingData);

        try {
            $courseOffering = CourseOffering::create($courseOfferingData);
        } catch (\Exception $e) {
            Log::error("Failed to create course offering", [
                'error' => $e->getMessage(),
                'data' => $courseOfferingData,
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }

        // Requirements: 1.5 - Include semester linkage, lecturer assignment, delivery mode, schedule, location, syllabus, and assessment components
        try {
            Log::info("Assigning lecturers");
            $this->assignLecturers(collect([$courseOffering]));

            Log::info("Creating syllabus data");
            $this->createSyllabusData($courseOffering);

            Log::info("Assigning schedule and location");
            $this->assignScheduleAndLocation($courseOffering, $configuration); // Use the same configuration
        } catch (\Exception $e) {
            Log::error("Failed in post-creation setup", [
                'error' => $e->getMessage(),
                'course_offering_id' => $courseOffering->id,
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }

        Log::info("Created course offering for {$curriculumUnit->unit->code} in {$semester->code}");

        return $courseOffering;
    }

    /**
     * Process course offerings for a specific semester
     */
    private function processSemester(Semester $semester, SeederConfiguration $configuration): void
    {
        Log::info("Processing semester: {$semester->code}");

        // Generate course offerings using the new method
        $createdOfferings = $this->generateCourseOfferings($semester, $configuration);

        $createdSyllabi = 0;
        $createdAssessments = 0;

        // Process each created offering for additional setup
        foreach ($createdOfferings as $courseOffering) {
            try {
                // Create or update syllabus
                $syllabus = $this->createOrUpdateSyllabus($courseOffering->curriculumUnit, $semester);
                if ($syllabus && $syllabus->wasRecentlyCreated) {
                    $createdSyllabi++;
                }

                // Create assessment components if needed
                if ($syllabus) {
                    $assessmentCount = $this->createAssessmentComponents($syllabus, $configuration);
                    $createdAssessments += $assessmentCount;
                }

                // Assign realistic schedule and location data
                $this->assignScheduleAndLocation($courseOffering, $configuration);
            } catch (\Exception $e) {
                Log::warning("Failed to complete setup for course offering {$courseOffering->id}: " . $e->getMessage());
            }
        }

        $this->addRecordCount('course_offerings', $createdOfferings->count());
        $this->addRecordCount('syllabi', $createdSyllabi);
        $this->addRecordCount('assessment_components', $createdAssessments);

        Log::info("Semester processing complete", [
            'semester' => $semester->code,
            'course_offerings' => $createdOfferings->count(),
            'syllabi' => $createdSyllabi,
            'assessment_components' => $createdAssessments,
        ]);
    }

    /**
     * Assign lecturers to course offerings
     * Requirements: 1.4 - Add lecturer assignment logic with dummy data generation
     */
    public function assignLecturers(Collection $offerings): void
    {
        foreach ($offerings as $offering) {
            if (!$offering->lecture_id) {
                $lecturer = $this->assignLecturer($offering->curriculumUnit);
                if ($lecturer) {
                    $offering->update(['lecture_id' => $lecturer->id]);
                }
            }
        }
    }

    /**
     * Create syllabus data for course offering
     * Requirements: 1.2 - Create syllabus generation for course offerings without existing syllabus
     */
    public function createSyllabusData(CourseOffering $offering): Syllabus
    {
        $curriculumUnit = $offering->curriculumUnit;

        // Check if syllabus already exists
        $existingSyllabus = Syllabus::where('curriculum_unit_id', $curriculumUnit->id)
            ->where('is_active', true)
            ->first();

        if ($existingSyllabus) {
            return $existingSyllabus;
        }

        // Create new syllabus with realistic data
        $totalHours = $this->dataGenerator->getRealisticTotalHours($curriculumUnit);
        $hoursPerSession = $this->dataGenerator->getRealisticHoursPerSession($curriculumUnit);

        $syllabus = Syllabus::create([
            'curriculum_unit_id' => $curriculumUnit->id,
            'version' => '1.0',
            'description' => "Syllabus for {$curriculumUnit->unit->name}",
            'total_hours' => $totalHours,
            'hours_per_session' => $hoursPerSession,
            'is_active' => true,
        ]);

        Log::info("Created syllabus for curriculum unit {$curriculumUnit->id}");

        return $syllabus;
    }

    /**
     * Create assessment components for syllabus
     * Requirements: 1.2 - Generate assessment components with realistic weight distribution
     * Ensure total assessment weights equal 100% for each syllabus
     */
    private function createAssessmentComponents(Syllabus $syllabus, SeederConfiguration $configuration): int
    {
        // Check if assessment components already exist
        $existingComponents = $syllabus->assessmentComponents()->count();
        if ($existingComponents > 0) {
            return 0; // Already has components
        }

        // Generate realistic assessment structure
        $assessmentStructure = $this->dataGenerator->generateAssessmentStructure($syllabus);

        // Validate that weights sum to 100%
        $totalWeight = array_sum(array_column($assessmentStructure, 'weight_percentage'));
        if ($totalWeight !== 100) {
            Log::warning("Assessment structure weights do not sum to 100%", [
                'syllabus_id' => $syllabus->id,
                'total_weight' => $totalWeight,
            ]);

            // Adjust weights to sum to 100%
            $assessmentStructure = $this->normalizeAssessmentWeights($assessmentStructure);
        }

        $createdCount = 0;
        foreach ($assessmentStructure as $componentData) {
            AssessmentComponent::create([
                'syllabus_id' => $syllabus->id,
                'name' => $componentData['name'],
                'type' => $componentData['type'],
                'weight_percentage' => $componentData['weight_percentage'],
                'description' => $componentData['description'] ?? null,
            ]);
            $createdCount++;
        }

        // Verify total weight equals 100%
        $actualTotal = AssessmentComponent::where('syllabus_id', $syllabus->id)
            ->sum('weight_percentage');

        Log::info("Created assessment components", [
            'syllabus_id' => $syllabus->id,
            'count' => $createdCount,
            'total_weight' => $actualTotal,
        ]);

        return $createdCount;
    }

    /**
     * Normalize assessment weights to sum to 100%
     */
    private function normalizeAssessmentWeights(array $assessmentStructure): array
    {
        $totalWeight = array_sum(array_column($assessmentStructure, 'weight_percentage'));

        if ($totalWeight === 0) {
            // Equal distribution if all weights are 0
            $equalWeight = 100 / count($assessmentStructure);
            foreach ($assessmentStructure as &$component) {
                $component['weight_percentage'] = round($equalWeight, 1);
            }
        } else {
            // Scale proportionally to sum to 100
            foreach ($assessmentStructure as &$component) {
                $component['weight_percentage'] = round(
                    ($component['weight_percentage'] / $totalWeight) * 100,
                    1
                );
            }
        }

        // Handle rounding errors to ensure exact 100%
        $newTotal = array_sum(array_column($assessmentStructure, 'weight_percentage'));
        if ($newTotal !== 100.0) {
            $diff = 100.0 - $newTotal;
            $assessmentStructure[0]['weight_percentage'] += $diff;
        }

        return $assessmentStructure;
    }

    /**
     * Assign schedule and location to course offering
     * Requirements: 1.2 - Implement delivery mode assignment (in_person, online, hybrid)
     * Generate realistic schedule days and time slots
     * Assign location data for in-person courses
     */
    private function assignScheduleAndLocation(CourseOffering $courseOffering, SeederConfiguration $configuration): void
    {
        // Assign delivery mode if not already set
        if (empty($courseOffering->delivery_mode) || $courseOffering->delivery_mode === 'in_person') {
            $deliveryMode = $this->assignDeliveryMode($courseOffering);
            $courseOffering->update(['delivery_mode' => $deliveryMode]);
        }

        // Generate realistic schedule based on delivery mode
        $schedule = $this->dataGenerator->generateRealisticSchedule($courseOffering->delivery_mode);

        // Assign location for in-person or hybrid courses
        $location = null;
        if (in_array($courseOffering->delivery_mode, ['in_person', 'hybrid'])) {
            $location = $this->assignLocation($courseOffering);
        } else {
            $location = 'Online'; // For online courses
        }

        $courseOffering->update([
            'schedule_days' => $schedule['days'],
            'schedule_time_start' => $schedule['start_time'],
            'schedule_time_end' => $schedule['end_time'],
            'location' => $location,
        ]);

        Log::info("Assigned schedule and location", [
            'course_offering_id' => $courseOffering->id,
            'delivery_mode' => $courseOffering->delivery_mode,
            'days' => $schedule['days'],
            'time' => $schedule['start_time'] . ' - ' . $schedule['end_time'],
            'location' => $location,
        ]);
    }

    /**
     * Assign delivery mode for course offering
     * Requirements: 1.2 - Implement delivery mode assignment (in_person, online, hybrid)
     */
    private function assignDeliveryMode(CourseOffering $courseOffering): string
    {
        // Default distribution: in_person 70%, online 20%, hybrid 10%
        $deliveryModeWeights = [
            'in_person' => 70,
            'online' => 20,
            'hybrid' => 10,
        ];

        // Adjust based on unit type if available
        $unitType = $courseOffering->curriculumUnit?->type;
        if ($unitType === 'practical' || $unitType === 'lab') {
            // Labs and practical units are typically in-person
            $deliveryModeWeights = [
                'in_person' => 80,
                'hybrid' => 20,
                'online' => 0,
            ];
        } elseif ($unitType === 'theory') {
            // Theory units can be more flexible
            $deliveryModeWeights = [
                'in_person' => 50,
                'online' => 30,
                'hybrid' => 20,
            ];
        }

        return $this->getWeightedRandomChoice($deliveryModeWeights);
    }

    /**
     * Get weighted random choice from array of weights
     */
    private function getWeightedRandomChoice(array $weights): string
    {
        $totalWeight = array_sum($weights);
        $random = mt_rand(1, $totalWeight);

        $current = 0;
        foreach ($weights as $choice => $weight) {
            $current += $weight;
            if ($random <= $current) {
                return $choice;
            }
        }

        // Fallback to first choice
        return array_key_first($weights);
    }

    /**
     * Assign a room location for in-person courses
     */
    private function assignLocation(CourseOffering $courseOffering): ?string
    {
        // Get available rooms directly (avoid problematic relationship)
        $rooms = Room::take(50)->get();

        if ($rooms->isEmpty()) {
            return 'TBA'; // To be assigned
        }

        $room = $rooms->random();
        return ($room->building ? $room->building . ' - ' : '') . $room->name;
    }

    /**
     * Get semester by code
     */
    private function getSemester(string $semesterCode): ?Semester
    {
        return Semester::where('code', $semesterCode)->first();
    }

    /**
     * Assign a lecturer to a course offering
     */
    private function assignLecturer(CurriculumUnit $curriculumUnit): ?Lecture
    {
        // Get available lecturers
        $availableLecturers = Lecture::active()
            ->availableForAssignment()
            ->get();

        if ($availableLecturers->isEmpty()) {
            Log::warning("No available lecturers found for curriculum unit {$curriculumUnit->id}");
            return null;
        }

        // For now, assign randomly - could be improved with expertise matching
        return $availableLecturers->random();
    }

    /**
     * Create or update syllabus for curriculum unit
     */
    private function createOrUpdateSyllabus(CurriculumUnit $curriculumUnit, Semester $semester): Syllabus
    {
        // Check if syllabus already exists
        $existingSyllabus = Syllabus::where('curriculum_unit_id', $curriculumUnit->id)
            ->where('is_active', true)
            ->first();

        if ($existingSyllabus) {
            return $existingSyllabus;
        }

        // Create new syllabus
        $syllabus = Syllabus::create([
            'curriculum_unit_id' => $curriculumUnit->id,
            'version' => '1.0',
            'description' => 'Generated syllabus for ' . $curriculumUnit->unit->name,
            'total_hours' => $this->dataGenerator->getRealisticTotalHours($curriculumUnit),
            'hours_per_session' => $this->dataGenerator->getRealisticHoursPerSession($curriculumUnit),
            'is_active' => true,
        ]);

        Log::debug("Created syllabus", [
            'id' => $syllabus->id,
            'curriculum_unit_id' => $curriculumUnit->id,
            'total_hours' => $syllabus->total_hours,
        ]);

        return $syllabus;
    }

    /**
     * Validate prerequisites for this phase
     * CRITICAL: Ensure enrollments exist before creating course offerings
     */
    public function validatePrerequisites(): bool
    {
        // Check if we have enrollments (CRITICAL for enrollment-driven approach)
        $enrollmentsCount = Enrollment::where('status', 'in_progress')->count();
        if ($enrollmentsCount === 0) {
            Log::warning('No active enrollments found - course offerings cannot be created without enrollment demand');
            return false;
        }

        // Check if we have necessary data
        $studentsCount = Student::where('academic_status', 'active')->count();
        if ($studentsCount === 0) {
            Log::warning('No active students found for course offering generation');
            return false;
        }

        $lecturersCount = Lecture::active()->count();
        if ($lecturersCount === 0) {
            Log::warning('No active lecturers found for course offering generation');
            // Don't fail - we can create offerings without lecturers initially
        }

        Log::info("Course offering prerequisites validated", [
            'enrollments' => $enrollmentsCount,
            'active_students' => $studentsCount,
            'active_lecturers' => $lecturersCount,
        ]);

        return true;
    }

    /**
     * Rollback the phase execution
     */
    public function rollback(): void
    {
        // Implementation for rollback would go here
        // For now, we'll log the rollback attempt
        Log::info("Rollback requested for course offerings phase");

        // In a real implementation, you might:
        // 1. Delete created course offerings
        // 2. Delete created syllabi
        // 3. Delete created assessment components
        // 4. But preserve existing data that wasn't created by this seeder
    }
}
