<?php

declare(strict_types=1);

namespace App\Services\AcademicLifecycleSeeder\Processors;

use App\Models\Student;
use App\Models\CourseOffering;
use App\Models\CourseRegistration;
use App\Models\AcademicRecord;
use App\Models\Semester;
use App\Models\CurriculumUnit;
use App\Services\AcademicLifecycleSeeder\SeederConfiguration;
use App\Services\AcademicLifecycleSeeder\Generators\RealisticDataGenerator;
use App\Services\AcademicLifecycleSeeder\Exceptions\PhaseExecutionException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class StudentRegistrationProcessor extends AbstractPhaseProcessor
{
    private RealisticDataGenerator $dataGenerator;

    public function __construct()
    {
        parent::__construct('student_registration');
        $this->dataGenerator = new RealisticDataGenerator();
    }

    /**
     * Process the student registration phase
     */
    protected function process(SeederConfiguration $configuration): bool
    {
        Log::info('Starting student registration phase');

        foreach ($configuration->targetSemesters as $semesterCode) {
            $semester = $this->getSemester($semesterCode);
            if (!$semester) {
                throw new PhaseExecutionException(
                    "Semester not found: {$semesterCode}",
                    0,
                    null,
                    $this->phaseName
                );
            }

            // Get course offerings for this semester
            $courseOfferings = CourseOffering::where('semester_id', $semester->id)
                ->where('is_active', true)
                ->with(['curriculumUnit.unit', 'semester'])
                ->get();

            if ($courseOfferings->isEmpty()) {
                Log::warning("No course offerings found for semester {$semester->code}");
                continue;
            }

            // Register students for courses
            $this->registerStudentsForCourses($courseOfferings);

            // Handle group assignment for large courses
            $this->processGroupAssignment($courseOfferings, $configuration);
        }

        Log::info('Student registration phase completed successfully');
        return true;
    }

    /**
     * Register students for appropriate courses
     * Requirements: 2.1 - Match students with course offerings based on their curriculum version
     */
    public function registerStudentsForCourses(Collection $offerings): void
    {
        Log::info("Registering students for {$offerings->count()} course offerings");

        $totalRegistrations = 0;
        $totalAcademicRecords = 0;

        foreach ($offerings as $courseOffering) {
            $result = $this->registerStudentsForCourseOffering($courseOffering);
            $totalRegistrations += $result['registrations_created'];
            $totalAcademicRecords += $result['academic_records_created'];
        }

        $this->addRecordCount('course_registrations', $totalRegistrations);
        $this->addRecordCount('academic_records', $totalAcademicRecords);

        Log::info("Student registration completed", [
            'total_registrations' => $totalRegistrations,
            'total_academic_records' => $totalAcademicRecords,
        ]);
    }

    /**
     * Create academic records for registrations
     * Requirements: 2.2 - Generate corresponding academic records with "in_progress" status
     */
    public function createAcademicRecords(Collection $registrations): void
    {
        $createdRecords = 0;

        foreach ($registrations as $registration) {
            try {
                $academicRecord = $this->createAcademicRecordForRegistration($registration);
                if ($academicRecord) {
                    $createdRecords++;
                }
            } catch (\Exception $e) {
                Log::warning("Failed to create academic record for registration {$registration->id}: " . $e->getMessage());
            }
        }

        $this->addRecordCount('academic_records', $createdRecords);
        Log::info("Created {$createdRecords} academic records");
    }

    /**
     * Create academic record for a registration
     */
    private function createAcademicRecordForRegistration(CourseRegistration $registration): ?AcademicRecord
    {
        return $this->createAcademicRecord(
            $registration->student,
            $registration->courseOffering,
            $registration->semester,
            $registration->courseOffering->curriculumUnit,
            $registration
        );
    }

    /**
     * Get semester by code
     */
    private function getSemester(string $semesterCode): ?Semester
    {
        return Semester::where('code', $semesterCode)->first();
    }

    /**
     * Process registrations for a specific semester
     */
    private function processSemesterRegistrations(Semester $semester, SeederConfiguration $configuration): void
    {
        Log::info("Processing registrations for semester: {$semester->code}");

        // Get all active students
        $students = Student::where('status', 'active')
            ->whereNotNull('curriculum_version_id')
            ->with(['curriculumVersion', 'campus', 'program'])
            ->get();

        if ($students->isEmpty()) {
            Log::warning("No active students found for registration");
            return;
        }

        // Get available course offerings for this semester
        $courseOfferings = CourseOffering::where('semester_id', $semester->id)
            ->where('is_active', true)
            ->with(['curriculumUnit.unit', 'semester'])
            ->get()
            ->keyBy('curriculum_unit_id');

        if ($courseOfferings->isEmpty()) {
            Log::warning("No course offerings found for semester {$semester->code}");
            return;
        }

        $registrationsCreated = 0;
        $academicRecordsCreated = 0;

        // Process each student
        foreach ($students as $student) {
            $studentResult = $this->registerStudentForCourses($student, $semester, $courseOfferings, $configuration);
            $registrationsCreated += $studentResult['registrations_created'];
            $academicRecordsCreated += $studentResult['academic_records_created'];
        }

        $this->addRecordCount('course_registrations', $registrationsCreated);
        $this->addRecordCount('academic_records', $academicRecordsCreated);

        Log::info("Semester {$semester->code} completed: {$registrationsCreated} registrations, {$academicRecordsCreated} academic records");
    }

    /**
     * Register a student for appropriate courses
     */
    private function registerStudentForCourses(Student $student, Semester $semester, Collection $courseOfferings, SeederConfiguration $configuration): array
    {
        $registrationsCreated = 0;
        $academicRecordsCreated = 0;

        // Get curriculum units for this student that should be taken in current semester
        $eligibleCurriculumUnits = $this->getEligibleCurriculumUnits($student, $semester);

        if ($eligibleCurriculumUnits->isEmpty()) {
            return [
                'registrations_created' => 0,
                'academic_records_created' => 0,
            ];
        }

        foreach ($eligibleCurriculumUnits as $curriculumUnit) {
            // Check if course offering exists for this curriculum unit
            if (!$courseOfferings->has($curriculumUnit->id)) {
                continue;
            }

            $courseOffering = $courseOfferings->get($curriculumUnit->id);

            // Check if student is already registered for this course
            $existingRegistration = CourseRegistration::where('student_id', $student->id)
                ->where('course_offering_id', $courseOffering->id)
                ->where('semester_id', $semester->id)
                ->exists();

            if ($existingRegistration) {
                continue;
            }

            // Validate prerequisites
            if (!$this->validateStudentPrerequisites($student, $curriculumUnit)) {
                continue;
            }

            // Check capacity (with some realistic overbooking)
            if (!$this->checkCourseCapacity($courseOffering, $configuration)) {
                continue;
            }

            try {
                // Create course registration
                $registration = $this->createCourseRegistration($student, $courseOffering, $semester, $curriculumUnit);
                $registrationsCreated++;

                // Create corresponding academic record
                $academicRecord = $this->createAcademicRecord($student, $courseOffering, $semester, $curriculumUnit, $registration);
                $academicRecordsCreated++;

                // Update course offering enrollment count
                $courseOffering->increment('current_enrollment');
            } catch (\Exception $e) {
                Log::error("Failed to register student {$student->student_id} for course {$curriculumUnit->unit->unit_code}: " . $e->getMessage());
                continue;
            }
        }

        return [
            'registrations_created' => $registrationsCreated,
            'academic_records_created' => $academicRecordsCreated,
        ];
    }

    /**
     * Get eligible curriculum units for a student in a semester
     */
    private function getEligibleCurriculumUnits(Student $student, Semester $semester): Collection
    {
        // Determine semester number for this student
        $semesterNumber = $this->calculateStudentSemesterNumber($student);

        // Get curriculum units for this semester number in student's curriculum version
        $curriculumUnits = CurriculumUnit::where('curriculum_version_id', $student->curriculum_version_id)
            ->where('semester_number', $semesterNumber)
            ->with(['unit'])
            ->get();

        // Filter out units that the student has already completed successfully
        $completedUnitIds = AcademicRecord::where('student_id', $student->id)
            ->whereIn('grade_status', ['completed', 'passed'])
            ->pluck('unit_id')
            ->toArray();

        return $curriculumUnits->filter(function ($curriculumUnit) use ($completedUnitIds) {
            return !in_array($curriculumUnit->unit_id, $completedUnitIds);
        });
    }

    /**
     * Calculate student's current semester number (using same logic as CourseOfferingProcessor)
     */
    private function calculateStudentSemesterNumber(Student $student): int
    {
        $admissionDate = $student->admission_date;

        // Get the currently active semester
        $activeSemester = Semester::where('is_active', true)
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->first();

        if (!$activeSemester) {
            // If no active semester, use the latest semester
            $activeSemester = Semester::orderBy('start_date', 'desc')->first();
        }

        if (!$activeSemester) {
            return 1; // Default to first semester if no semesters found
        }

        // Get all semesters from admission date up to and including the active semester
        $semesters = Semester::where('start_date', '>=', $admissionDate)
            ->where('start_date', '<=', $activeSemester->start_date)
            ->orderBy('start_date', 'asc')
            ->get();

        // The semester number is the count of semesters since admission
        $semesterNumber = $semesters->count();

        // Ensure minimum semester number is 1
        return max(1, $semesterNumber);
    }

    /**
     * Validate prerequisites for a student and curriculum unit
     */
    private function validateStudentPrerequisites(Student $student, CurriculumUnit $curriculumUnit): bool
    {
        $unit = $curriculumUnit->unit;

        // If no prerequisites defined, student is eligible
        if (empty($unit->prerequisites)) {
            return true;
        }

        // Get completed units for this student
        $completedUnitCodes = AcademicRecord::where('student_id', $student->id)
            ->whereIn('grade_status', ['completed', 'passed'])
            ->join('units', 'academic_records.unit_id', '=', 'units.id')
            ->pluck('units.unit_code')
            ->toArray();

        // Parse prerequisites (simple implementation - assumes comma-separated unit codes)
        $prerequisiteUnits = array_map('trim', explode(',', $unit->prerequisites));

        // Check if all prerequisite units are completed
        foreach ($prerequisiteUnits as $prerequisiteUnit) {
            if (!in_array($prerequisiteUnit, $completedUnitCodes)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if course has available capacity
     */
    private function checkCourseCapacity(CourseOffering $courseOffering, SeederConfiguration $configuration): bool
    {
        $allowOverbooking = $configuration->allowOverbooking ?? true;
        $overbookingPercentage = 0.1; // 10% overbooking

        if (!$allowOverbooking) {
            return $courseOffering->current_enrollment < $courseOffering->max_capacity;
        }

        $maxWithOverbooking = $courseOffering->max_capacity * (1 + $overbookingPercentage);
        return $courseOffering->current_enrollment < $maxWithOverbooking;
    }

    /**
     * Create a course registration record
     */
    private function createCourseRegistration(Student $student, CourseOffering $courseOffering, Semester $semester, CurriculumUnit $curriculumUnit): CourseRegistration
    {
        $registrationDate = $this->generateRegistrationDate($semester);
        $registrationMethod = $this->dataGenerator->getRandomRegistrationMethod();

        return CourseRegistration::create([
            'student_id' => $student->id,
            'course_offering_id' => $courseOffering->id,
            'semester_id' => $semester->id,
            'registration_status' => 'registered',
            'registration_date' => $registrationDate,
            'registration_method' => $registrationMethod,
            'credit_hours' => $curriculumUnit->unit->credit_points ?? 3,
            'attempt_number' => $this->calculateAttemptNumber($student, $curriculumUnit->unit_id),
            'is_retake' => $this->isRetakeAttempt($student, $curriculumUnit->unit_id),
            'notes' => 'Generated by Academic Lifecycle Seeder',
        ]);
    }

    /**
     * Create an academic record for the registration
     */
    private function createAcademicRecord(Student $student, CourseOffering $courseOffering, Semester $semester, CurriculumUnit $curriculumUnit, CourseRegistration $registration): AcademicRecord
    {
        return AcademicRecord::create([
            'student_id' => $student->id,
            'course_offering_id' => $courseOffering->id,
            'semester_id' => $semester->id,
            'unit_id' => $curriculumUnit->unit_id,
            'program_id' => $student->program_id,
            'campus_id' => $student->campus_id,
            'credit_hours' => $curriculumUnit->unit->credit_points ?? 3,
            'grade_status' => 'in_progress',
            'completion_status' => 'enrolled',
            'enrollment_date' => $registration->registration_date,
            'is_repeat_course' => $registration->is_retake,
            'attempt_number' => $registration->attempt_number,
            'instructor_id' => $courseOffering->lecturer_id,
            'affects_academic_standing' => true,
            'affects_graduation_requirement' => true,
            'satisfies_prerequisite' => true,
            'administrative_notes' => 'Academic record created by Academic Lifecycle Seeder',
        ]);
    }

    /**
     * Generate a realistic registration date
     */
    private function generateRegistrationDate(Semester $semester): Carbon
    {
        // Registration typically happens 1-2 months before semester starts
        $registrationStart = $semester->start_date->copy()->subMonths(2);
        $registrationEnd = $semester->start_date->copy()->subWeeks(2);

        return $this->dataGenerator->getRandomDateBetween($registrationStart, $registrationEnd);
    }

    /**
     * Calculate attempt number for a student and unit
     */
    private function calculateAttemptNumber(Student $student, int $unitId): int
    {
        $attempts = AcademicRecord::where('student_id', $student->id)
            ->where('unit_id', $unitId)
            ->count();

        return $attempts + 1;
    }

    /**
     * Check if this is a retake attempt
     */
    private function isRetakeAttempt(Student $student, int $unitId): bool
    {
        return AcademicRecord::where('student_id', $student->id)
            ->where('unit_id', $unitId)
            ->exists();
    }

    /**
     * Register students for a specific course offering
     */
    private function registerStudentsForCourseOffering(CourseOffering $courseOffering): array
    {
        $registrationsCreated = 0;
        $academicRecordsCreated = 0;

        // Get students who should take this course based on their curriculum version
        $eligibleStudents = $this->getEligibleStudentsForCourse($courseOffering);

        foreach ($eligibleStudents as $student) {
            // Check if already registered
            $existingRegistration = CourseRegistration::where('student_id', $student->id)
                ->where('course_offering_id', $courseOffering->id)
                ->exists();

            if ($existingRegistration) {
                continue;
            }

            // Validate prerequisites
            if (!$this->validateStudentPrerequisites($student, $courseOffering->curriculumUnit)) {
                continue;
            }

            // Check capacity
            if ($courseOffering->current_enrollment >= $courseOffering->max_capacity) {
                continue;
            }

            try {
                // Create registration
                $registration = $this->createCourseRegistration(
                    $student,
                    $courseOffering,
                    $courseOffering->semester,
                    $courseOffering->curriculumUnit
                );
                $registrationsCreated++;

                // Create academic record
                $academicRecord = $this->createAcademicRecord(
                    $student,
                    $courseOffering,
                    $courseOffering->semester,
                    $courseOffering->curriculumUnit,
                    $registration
                );
                $academicRecordsCreated++;

                // Update enrollment count
                $courseOffering->increment('current_enrollment');
            } catch (\Exception $e) {
                Log::warning("Failed to register student {$student->student_id} for course {$courseOffering->curriculumUnit->unit->code}: " . $e->getMessage());
                continue;
            }
        }

        return [
            'registrations_created' => $registrationsCreated,
            'academic_records_created' => $academicRecordsCreated,
        ];
    }

    /**
     * Get eligible students for a course based on curriculum version matching
     */
    private function getEligibleStudentsForCourse(CourseOffering $courseOffering): Collection
    {
        $curriculumUnit = $courseOffering->curriculumUnit;

        // Get students with matching curriculum version
        $students = Student::where('academic_status', 'active')
            ->where('curriculum_version_id', $curriculumUnit->curriculum_version_id)
            ->with(['curriculumVersion'])
            ->get();

        // Filter students who should take this course in current semester
        return $students->filter(function ($student) use ($curriculumUnit) {
            $studentSemesterNumber = $this->calculateStudentSemesterNumber($student);

            // Student should take this course if it matches their current semester number
            return $curriculumUnit->semester_number === $studentSemesterNumber;
        });
    }

    /**
     * Process group assignment for large courses
     * Requirements: 2.3 - Automatically split large courses into multiple sections with maximum 55 students each
     */
    private function processGroupAssignment(Collection $courseOfferings, SeederConfiguration $configuration): void
    {
        Log::info("Processing group assignment for large courses");

        $maxStudentsPerSection = $configuration->maxStudentsPerSection;
        $sectionsCreated = 0;
        $studentsReassigned = 0;

        foreach ($courseOfferings as $courseOffering) {
            if ($courseOffering->current_enrollment > $maxStudentsPerSection) {
                $result = $this->splitLargeCourse($courseOffering, $maxStudentsPerSection);
                $sectionsCreated += $result['sections_created'];
                $studentsReassigned += $result['students_reassigned'];
            }
        }

        $this->addRecordCount('course_sections', $sectionsCreated);
        $this->addRecordCount('students_reassigned', $studentsReassigned);

        Log::info("Group assignment completed", [
            'sections_created' => $sectionsCreated,
            'students_reassigned' => $studentsReassigned,
        ]);
    }

    /**
     * Split a large course into multiple sections
     * Requirements: 2.3 - Split courses with more than 55 students into sections with section codes (A, B, C)
     */
    private function splitLargeCourse(CourseOffering $originalCourseOffering, int $maxStudentsPerSection): array
    {
        $totalStudents = $originalCourseOffering->current_enrollment;
        $sectionsNeeded = ceil($totalStudents / $maxStudentsPerSection);

        if ($sectionsNeeded <= 1) {
            return ['sections_created' => 0, 'students_reassigned' => 0];
        }

        Log::info("Splitting course offering {$originalCourseOffering->id} into {$sectionsNeeded} sections", [
            'unit_code' => $originalCourseOffering->curriculumUnit->unit->code,
            'total_students' => $totalStudents,
        ]);

        $sectionLetters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'];
        $sectionsCreated = 0;
        $studentsReassigned = 0;

        // Update original course offering to be section A
        $originalCourseOffering->update([
            'section_code' => $sectionLetters[0],
            'max_capacity' => $maxStudentsPerSection,
        ]);

        // Get all registrations for this course
        $registrations = CourseRegistration::where('course_offering_id', $originalCourseOffering->id)
            ->with('student')
            ->get();

        // Create additional sections
        for ($i = 1; $i < $sectionsNeeded; $i++) {
            $newSection = $this->createCourseSectionOffering($originalCourseOffering, $sectionLetters[$i], $maxStudentsPerSection);
            $sectionsCreated++;

            // Reassign students to new section
            $studentsForThisSection = $registrations->slice($i * $maxStudentsPerSection, $maxStudentsPerSection);

            foreach ($studentsForThisSection as $registration) {
                $this->reassignStudentToSection($registration, $newSection);
                $studentsReassigned++;
            }
        }

        // Update enrollment counts for all sections
        $this->updateSectionEnrollmentCounts($originalCourseOffering, $registrations, $maxStudentsPerSection);

        return ['sections_created' => $sectionsCreated, 'students_reassigned' => $studentsReassigned];
    }

    /**
     * Create a new course section offering
     */
    private function createCourseSectionOffering(CourseOffering $originalOffering, string $sectionCode, int $maxCapacity): CourseOffering
    {
        return CourseOffering::create([
            'semester_id' => $originalOffering->semester_id,
            'curriculum_unit_id' => $originalOffering->curriculum_unit_id,
            'lecture_id' => $originalOffering->lecture_id, // Can assign different lecturer later
            'section_code' => $sectionCode,
            'max_capacity' => $maxCapacity,
            'current_enrollment' => 0, // Will be updated when students are reassigned
            'waitlist_capacity' => intval($maxCapacity * 0.2),
            'current_waitlist' => 0,
            'delivery_mode' => $originalOffering->delivery_mode,
            'is_active' => true,
            'enrollment_status' => $originalOffering->enrollment_status,
            'registration_start_date' => $originalOffering->registration_start_date,
            'registration_end_date' => $originalOffering->registration_end_date,
            'add_drop_deadline' => $originalOffering->add_drop_deadline,
            'withdrawal_deadline' => $originalOffering->withdrawal_deadline,
            'schedule_days' => $originalOffering->schedule_days,
            'schedule_time_start' => $originalOffering->schedule_time_start,
            'schedule_time_end' => $originalOffering->schedule_time_end,
            'location' => $originalOffering->location . " (Section {$sectionCode})",
        ]);
    }

    /**
     * Reassign student registration to new section
     * Requirements: 2.3 - Update course_registration.course_offering_id for reassigned students
     */
    private function reassignStudentToSection(CourseRegistration $registration, CourseOffering $newSection): void
    {
        // Update the registration to point to the new section
        $registration->update(['course_offering_id' => $newSection->id]);

        // Update any associated academic records
        AcademicRecord::where('student_id', $registration->student_id)
            ->where('course_offering_id', $registration->course_offering_id)
            ->where('semester_id', $registration->semester_id)
            ->update(['course_offering_id' => $newSection->id]);

        Log::debug("Reassigned student {$registration->student->student_id} to section {$newSection->section_code}");
    }

    /**
     * Update enrollment counts for all sections
     * Requirements: 2.3 - Maintain enrollment balance across sections
     */
    private function updateSectionEnrollmentCounts(CourseOffering $originalOffering, Collection $registrations, int $maxStudentsPerSection): void
    {
        // Get all sections for this curriculum unit and semester
        $allSections = CourseOffering::where('curriculum_unit_id', $originalOffering->curriculum_unit_id)
            ->where('semester_id', $originalOffering->semester_id)
            ->get();

        foreach ($allSections as $section) {
            $enrollmentCount = CourseRegistration::where('course_offering_id', $section->id)->count();
            $section->update(['current_enrollment' => $enrollmentCount]);
        }
    }

    /**
     * Rollback the student registration phase
     */
    public function rollback(): void
    {
        Log::info("Rolling back student registration phase: {$this->phaseName}");

        // Note: Rollback implementation would be complex as it needs to identify
        // which records were created by the seeder vs. manual entries
        // For now, we log the rollback request
    }
}
