<?php

declare(strict_types=1);

namespace App\Services\AcademicLifecycleSeeder\Processors;

use App\Models\Student;
use App\Models\Semester;
use App\Models\AcademicRecord;
use App\Models\GpaCalculation;
use App\Models\AcademicStanding;
use App\Services\AcademicLifecycleSeeder\SeederConfiguration;
use App\Services\AcademicLifecycleSeeder\Exceptions\PhaseExecutionException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * GpaStandingProcessor
 * 
 * Calculates semester and cumulative GPAs and determines academic standings.
 * 
 * Requirements:
 * - Calculate semester GPA for each student based on completed courses
 * - Calculate cumulative GPA including previous semester data
 * - Generate gpa_calculations records with both semester and cumulative types
 * - Implement GPA threshold logic (probation < 2.0, good >= 2.0, honors >= 3.5)
 * - Create academic_standings records with appropriate status
 * - Set effective_date, reason, and created_by fields
 */
class GpaStandingProcessor extends AbstractPhaseProcessor
{
    public function __construct()
    {
        parent::__construct('gpa_standing');
    }

    /**
     * Process the GPA and academic standing phase
     */
    protected function process(SeederConfiguration $configuration): bool
    {
        Log::info('Starting GPA and academic standing calculation phase');

        foreach ($configuration->targetSemesters as $semesterCode) {
            $this->processSemesterGpaStanding($semesterCode, $configuration);
        }

        Log::info('GPA and academic standing calculation phase completed successfully');
        return true;
    }

    /**
     * Process GPA and standing calculations for a specific semester
     */
    private function processSemesterGpaStanding(string $semesterCode, SeederConfiguration $configuration): void
    {
        $semester = Semester::where('code', $semesterCode)->first();
        
        if (!$semester) {
            Log::warning("Semester not found: {$semesterCode}");
            return;
        }

        // Get all students who have completed courses in this semester
        $studentsWithCompletedCourses = Student::whereHas('academicRecords', function ($query) use ($semester) {
            $query->where('semester_id', $semester->id)
                  ->whereIn('completion_status', ['completed', 'failed']);
        })
        ->with(['academicRecords'])
        ->get();

        if ($studentsWithCompletedCourses->isEmpty()) {
            Log::warning("No students with completed courses found for semester {$semesterCode}");
            return;
        }

        $totalGpaCalculations = 0;
        $totalStandings = 0;

        foreach ($studentsWithCompletedCourses as $student) {
            // Calculate semester GPA
            $semesterGpa = $this->calculateSemesterGpa($student, $semester);
            if ($semesterGpa !== null) {
                $this->createGpaCalculation($student, $semester, 'semester', $semesterGpa);
                $totalGpaCalculations++;
            }

            // Calculate cumulative GPA
            $cumulativeGpa = $this->calculateCumulativeGpa($student, $semester);
            if ($cumulativeGpa !== null) {
                $this->createGpaCalculation($student, $semester, 'cumulative', $cumulativeGpa);
                $totalGpaCalculations++;

                // Determine academic standing based on cumulative GPA
                $standing = $this->determineAcademicStanding($student, $semester, $cumulativeGpa);
                if ($standing) {
                    $totalStandings++;
                }
            }
        }

        $this->addRecordCount('gpa_calculations', $totalGpaCalculations);
        $this->addRecordCount('academic_standings', $totalStandings);

        Log::info("GPA and standing calculations completed for semester {$semesterCode}", [
            'students_processed' => $studentsWithCompletedCourses->count(),
            'gpa_calculations_created' => $totalGpaCalculations,
            'academic_standings_created' => $totalStandings,
        ]);
    }

    /**
     * Calculate semester GPA for a student
     * Requirements: 5.1 - Calculate semester GPA based on completed courses
     */
    public function calculateSemesterGpa(Student $student, Semester $semester): ?float
    {
        $semesterRecords = AcademicRecord::where('student_id', $student->id)
            ->where('semester_id', $semester->id)
            ->whereIn('completion_status', ['completed', 'failed'])
            ->whereNotNull('grade_points')
            ->whereNotNull('credit_hours')
            ->get();

        if ($semesterRecords->isEmpty()) {
            return null;
        }

        $totalQualityPoints = 0;
        $totalCreditHours = 0;

        foreach ($semesterRecords as $record) {
            $qualityPoints = $record->grade_points * $record->credit_hours;
            $totalQualityPoints += $qualityPoints;
            $totalCreditHours += $record->credit_hours;
        }

        if ($totalCreditHours === 0) {
            return null;
        }

        $gpa = round($totalQualityPoints / $totalCreditHours, 3);

        Log::debug("Calculated semester GPA for student {$student->student_id}", [
            'semester' => $semester->code,
            'total_quality_points' => $totalQualityPoints,
            'total_credit_hours' => $totalCreditHours,
            'gpa' => $gpa,
        ]);

        return $gpa;
    }

    /**
     * Calculate cumulative GPA for a student up to and including the specified semester
     * Requirements: 5.1 - Calculate cumulative GPA including previous semester data
     */
    public function calculateCumulativeGpa(Student $student, Semester $semester): ?float
    {
        // Get all completed academic records up to and including this semester
        $allRecords = AcademicRecord::where('student_id', $student->id)
            ->whereHas('semester', function ($query) use ($semester) {
                $query->where('start_date', '<=', $semester->start_date);
            })
            ->whereIn('completion_status', ['completed', 'failed'])
            ->whereNotNull('grade_points')
            ->whereNotNull('credit_hours')
            ->get();

        if ($allRecords->isEmpty()) {
            return null;
        }

        $totalQualityPoints = 0;
        $totalCreditHours = 0;

        foreach ($allRecords as $record) {
            $qualityPoints = $record->grade_points * $record->credit_hours;
            $totalQualityPoints += $qualityPoints;
            $totalCreditHours += $record->credit_hours;
        }

        if ($totalCreditHours === 0) {
            return null;
        }

        $cumulativeGpa = round($totalQualityPoints / $totalCreditHours, 3);

        Log::debug("Calculated cumulative GPA for student {$student->student_id}", [
            'up_to_semester' => $semester->code,
            'total_quality_points' => $totalQualityPoints,
            'total_credit_hours' => $totalCreditHours,
            'cumulative_gpa' => $cumulativeGpa,
        ]);

        return $cumulativeGpa;
    }

    /**
     * Create GPA calculation record
     * Requirements: 5.1 - Generate gpa_calculations records with both semester and cumulative types
     */
    public function createGpaCalculation(Student $student, Semester $semester, string $type, float $gpa): GpaCalculation
    {
        // Check if calculation already exists
        $existingCalculation = GpaCalculation::where('student_id', $student->id)
            ->where('semester_id', $semester->id)
            ->where('calculation_type', $type)
            ->first();

        if ($existingCalculation) {
            // Update existing calculation
            $existingCalculation->update([
                'gpa' => $gpa,
                'calculated_at' => now(),
            ]);
            return $existingCalculation;
        }

        // Create new calculation
        return GpaCalculation::create([
            'student_id' => $student->id,
            'semester_id' => $semester->id,
            'calculation_type' => $type,
            'gpa' => $gpa,
            'calculated_at' => now(),
            'notes' => 'Generated by Academic Lifecycle Seeder',
        ]);
    }

    /**
     * Determine academic standing based on GPA thresholds
     * Requirements: 5.2 - Implement GPA threshold logic and create academic_standings records
     */
    public function determineAcademicStanding(Student $student, Semester $semester, float $cumulativeGpa): ?AcademicStanding
    {
        // Determine standing based on GPA thresholds
        $standing = $this->getStandingFromGpa($cumulativeGpa);
        $reason = $this->getStandingReason($cumulativeGpa);

        // Check if standing has changed from previous semester
        $previousStanding = AcademicStanding::where('student_id', $student->id)
            ->whereHas('semester', function ($query) use ($semester) {
                $query->where('start_date', '<', $semester->start_date);
            })
            ->orderBy('effective_date', 'desc')
            ->first();

        // Only create new standing if it's different from the previous one
        if (!$previousStanding || $previousStanding->status !== $standing) {
            return $this->createAcademicStanding($student, $semester, $standing, $reason, $cumulativeGpa);
        }

        return null;
    }

    /**
     * Get academic standing from GPA
     */
    private function getStandingFromGpa(float $gpa): string
    {
        return match(true) {
            $gpa >= 3.5 => 'honors',
            $gpa >= 3.0 => 'dean_list',
            $gpa >= 2.0 => 'good_standing',
            $gpa >= 1.0 => 'probation',
            default => 'suspension',
        };
    }

    /**
     * Get reason for academic standing
     */
    private function getStandingReason(float $gpa): string
    {
        return match(true) {
            $gpa >= 3.5 => 'Cumulative GPA of 3.5 or higher qualifies for honors standing',
            $gpa >= 3.0 => 'Cumulative GPA of 3.0 or higher qualifies for dean\'s list',
            $gpa >= 2.0 => 'Cumulative GPA meets minimum requirements for good standing',
            $gpa >= 1.0 => 'Cumulative GPA below 2.0 results in academic probation',
            default => 'Cumulative GPA below 1.0 results in academic suspension',
        };
    }

    /**
     * Create academic standing record
     */
    private function createAcademicStanding(Student $student, Semester $semester, string $status, string $reason, float $gpa): AcademicStanding
    {
        return AcademicStanding::create([
            'student_id' => $student->id,
            'semester_id' => $semester->id,
            'status' => $status,
            'effective_date' => $semester->end_date,
            'reason' => $reason,
            'gpa_at_time' => $gpa,
            'created_by_user_id' => 1, // System user
            'notes' => 'Generated by Academic Lifecycle Seeder based on cumulative GPA',
        ]);
    }

    /**
     * Create intervention records for students with poor performance
     * Requirements: 5.2 - Create intervention records as appropriate
     */
    public function createInterventionRecords(Collection $standings): void
    {
        $interventionsCreated = 0;

        foreach ($standings as $standing) {
            if (in_array($standing->status, ['probation', 'suspension'])) {
                // Create academic hold or intervention
                $this->createAcademicIntervention($standing);
                $interventionsCreated++;
            }
        }

        $this->addRecordCount('academic_interventions', $interventionsCreated);
        Log::info("Created {$interventionsCreated} academic intervention records");
    }

    /**
     * Create academic intervention for at-risk students
     */
    private function createAcademicIntervention(AcademicStanding $standing): void
    {
        // This would create records in an academic_interventions table
        // For now, we'll just log the intervention
        Log::info("Academic intervention recommended", [
            'student_id' => $standing->student->student_id,
            'standing' => $standing->status,
            'gpa' => $standing->gpa_at_time,
            'semester' => $standing->semester->code,
        ]);
    }

    /**
     * Validate prerequisites for this phase
     */
    public function validatePrerequisites(): bool
    {
        // Check if we have completed academic records
        $completedRecordsCount = AcademicRecord::whereIn('completion_status', ['completed', 'failed'])
            ->whereNotNull('grade_points')
            ->count();

        if ($completedRecordsCount === 0) {
            Log::warning('No completed academic records found for GPA calculation');
            return false;
        }

        Log::info("GPA calculation prerequisites validated", [
            'completed_academic_records' => $completedRecordsCount,
        ]);

        return true;
    }

    /**
     * Rollback the GPA and standing calculation phase
     */
    public function rollback(): void
    {
        Log::info("Rolling back GPA and standing calculation phase");
        
        // In a real implementation, you would:
        // 1. Delete GPA calculations created by this seeder
        // 2. Delete academic standings created by this seeder
        // 3. Delete intervention records created by this seeder
        
        Log::warning("GPA and standing calculation rollback requested - manual cleanup may be required");
    }
}
